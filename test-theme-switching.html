<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Switching Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        [data-theme="light"] {
            background: #f0f0f0;
            color: #333;
        }
        
        [data-theme="dark"] {
            background: #222;
            color: #fff;
        }
        
        .test-box {
            width: 200px;
            height: 200px;
            margin: 20px;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        [data-theme="light"] .test-box {
            background: #8561C5;
        }
        
        [data-theme="dark"] .test-box {
            background: #8B5CF6;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
    </style>
</head>
<body data-theme="light">
    <h1>Theme Switching Test</h1>
    <p>Current theme: <span id="currentTheme">light</span></p>
    <button onclick="toggleTheme()">Toggle Theme</button>
    <div class="test-box"></div>
    
    <script>
        let currentTheme = 'light';
        
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.body.setAttribute('data-theme', currentTheme);
            document.getElementById('currentTheme').textContent = currentTheme;
            console.log('Theme switched to:', currentTheme);
        }
    </script>
</body>
</html>

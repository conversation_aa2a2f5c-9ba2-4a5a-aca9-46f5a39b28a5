<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Layered Background System</title>
    <meta name="color-scheme" content="light dark">
    
    <style>
        /* CSS Custom Properties for Theme Management */
        :root {
            /* Light Theme Variables */
            --bg-base-light: rgba(255, 255, 255, 0.30);
            --bg-overlay-light: linear-gradient(0deg, rgba(255, 255, 255, 0.50) 0%, rgba(255, 255, 255, 0.30) 100%);
            --blob-1-light: #8561C5;
            --blob-2-light: #BBBEC5;
            --blob-3-light: #2563EB;
            
            /* Dark Theme Variables */
            --bg-base-dark: rgba(10, 10, 20, 0.30);
            --bg-overlay-dark: rgba(10, 10, 20, 0.40);
            --blob-1-dark: #A855F7;
            --blob-2-dark: #8B5CF6;
            --blob-3-dark: #6366F1;
            
            /* Animation Variables */
            --transition-duration: 0.3s;
            --blur-amount: 250px;
            --backdrop-blur: 15px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            overflow-x: hidden;
        }

        /* Base Container Setup */
        .layered-background-container {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            transition: all var(--transition-duration) ease-in-out;
        }

        /* Layer 1: Base Background */
        .layered-background-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            transition: all var(--transition-duration) ease-in-out;
        }

        [data-theme="light"] .layered-background-container::before {
            background: var(--bg-base-light);
        }

        [data-theme="dark"] .layered-background-container::before {
            background: var(--bg-base-dark);
            backdrop-filter: blur(var(--backdrop-blur));
            -webkit-backdrop-filter: blur(var(--backdrop-blur));
        }

        /* Layer 2: Gradient Blobs */
        .gradient-blobs {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2;
            pointer-events: none;
        }

        .blob {
            position: absolute;
            filter: blur(var(--blur-amount));
            transition: all var(--transition-duration) ease-in-out;
        }

        .blob-1 {
            left: 0px;
            top: 191px;
            width: 477px;
            height: 381px;
            border-radius: 477px;
        }

        .blob-2 {
            left: 482px;
            top: 517px;
            width: 585px;
            height: 381px;
            border-radius: 585px;
        }

        .blob-3 {
            left: 1025px;
            top: 0px;
            width: 477px;
            height: 381px;
            border-radius: 477px;
        }

        /* Light theme blob colors */
        [data-theme="light"] .blob-1 { background: var(--blob-1-light); }
        [data-theme="light"] .blob-2 { background: var(--blob-2-light); }
        [data-theme="light"] .blob-3 { background: var(--blob-3-light); }

        /* Dark theme blob colors and positions */
        [data-theme="dark"] .blob-1 { background: var(--blob-1-dark); }
        [data-theme="dark"] .blob-2 { background: var(--blob-2-dark); }
        [data-theme="dark"] .blob-3 { background: var(--blob-3-dark); }

        /* Layer 3: Overlay */
        .layered-background-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 3;
            transition: all var(--transition-duration) ease-in-out;
        }

        [data-theme="light"] .layered-background-container::after {
            background: var(--bg-overlay-light);
            backdrop-filter: blur(var(--backdrop-blur));
            -webkit-backdrop-filter: blur(var(--backdrop-blur));
        }

        [data-theme="dark"] .layered-background-container::after {
            background: var(--bg-overlay-dark);
        }

        /* Content Styles */
        .content {
            position: relative;
            z-index: 10;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            text-align: center;
            padding: 2rem;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 3rem;
            max-width: 600px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all var(--transition-duration) ease-in-out;
        }

        [data-theme="dark"] .content-card {
            background: rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.05);
        }

        .title {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 1rem;
            color: #333;
            transition: color var(--transition-duration) ease-in-out;
        }

        [data-theme="dark"] .title {
            color: #fff;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: #666;
            transition: color var(--transition-duration) ease-in-out;
        }

        [data-theme="dark"] .subtitle {
            color: #ccc;
        }

        /* Paragraph text color for dark theme */
        [data-theme="dark"] .content-card p {
            color: #bbb !important;
        }

        /* Theme Toggle Button */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #333;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        [data-theme="dark"] .theme-toggle {
            color: #fff;
            background: rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 255, 255, 0.15);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        [data-theme="dark"] .theme-toggle:hover {
            background: rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 255, 255, 0.25);
        }

        /* Responsive Design */
        @media (max-width: 1440px) {
            .blob { transform: scale(0.8); }
            .blob-1 { left: -50px; top: 150px; }
            .blob-2 { left: 300px; top: 400px; }
            .blob-3 { left: 800px; top: -50px; }
        }

        @media (max-width: 768px) {
            .blob { 
                transform: scale(0.6); 
                filter: blur(150px);
            }
            .blob-1 { left: -100px; top: 100px; }
            .blob-2 { left: 150px; top: 300px; }
            .blob-3 { left: 400px; top: -100px; }
            
            .theme-toggle {
                top: 10px;
                right: 10px;
                padding: 8px 16px;
                font-size: 12px;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                transition: none !important;
            }
        }

        @media (prefers-contrast: high) {
            :root {
                --blur-amount: 100px;
                --backdrop-blur: 5px;
            }
        }
    </style>
</head>
<body data-theme="light">
    <div class="layered-background-container">
        <!-- Layer 2: Gradient Blobs -->
        <div class="gradient-blobs">
            <div class="blob blob-1"></div>
            <div class="blob blob-2"></div>
            <div class="blob blob-3"></div>
        </div>

        <!-- Theme Toggle -->
        <button class="theme-toggle" id="themeToggle">🌙 Dark Mode</button>

        <!-- Theme Debug Info -->
        <div style="position: fixed; top: 80px; right: 20px; z-index: 1000; background: rgba(0,0,0,0.5); color: white; padding: 10px; border-radius: 5px; font-size: 12px;" id="debugInfo">
            Theme: <span id="debugTheme">light</span>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="content-card">
                <h1 class="title">Layered Background System</h1>
                <p class="subtitle">Beautiful gradient blobs with smooth theme transitions</p>
                <p style="color: #888; font-size: 0.9rem; line-height: 1.6; transition: color var(--transition-duration) ease-in-out;">
                    This system uses CSS custom properties for instant theme switching,
                    backdrop filters for glass effects, and precise positioning for the perfect layout.
                    <br><br>
                    <strong>Press the button above to switch themes!</strong>
                </p>
            </div>
        </div>
    </div>

    <script>
        let currentTheme = 'light';
        const body = document.body;
        const themeToggle = document.getElementById('themeToggle');

        function updateTheme() {
            console.log('Updating theme to:', currentTheme);
            body.setAttribute('data-theme', currentTheme);
            themeToggle.textContent = currentTheme === 'light' ? '🌙 Dark Mode' : '☀️ Light Mode';

            // Force a repaint to ensure styles are applied
            body.style.display = 'none';
            body.offsetHeight; // Trigger reflow
            body.style.display = '';
        }

        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            console.log('Toggling theme to:', currentTheme);
            updateTheme();
        }

        // Initialize theme system
        function initializeTheme() {
            // Auto-detect system theme preference
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            if (mediaQuery.matches) {
                currentTheme = 'dark';
            }

            // Apply initial theme
            updateTheme();

            // Listen for system theme changes
            mediaQuery.addEventListener('change', (e) => {
                currentTheme = e.matches ? 'dark' : 'light';
                updateTheme();
            });
        }

        // Event listeners
        themeToggle.addEventListener('click', toggleTheme);

        // Keyboard shortcut (T key)
        document.addEventListener('keydown', (e) => {
            if (e.key === 't' || e.key === 'T') {
                toggleTheme();
            }
        });

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeTheme);
        } else {
            initializeTheme();
        }
    </script>
</body>
</html>
